import { notFound } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { blogPosts } from '@/data/blogData';
import CTA from '@/components/CTA';
import ReactMarkdown from 'react-markdown';

type BlogPostPageProps = {
  params: Promise<{ slug: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
};

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { slug } = await params;
  const post = blogPosts[slug];

  if (!post) {
    notFound();
  }

  // Generate structured data for the blog post
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    "headline": post.title,
    "description": post.excerpt,
    "image": {
      "@type": "ImageObject",
      "url": `https://www.gtafencingcompany.com${post.image.url}`,
      "width": post.image.width,
      "height": post.image.height,
      "alt": post.image.alt
    },
    "author": {
      "@type": "Organization",
      "name": post.author.name,
      "url": post.author.url
    },
    "publisher": {
      "@type": "Organization",
      "name": "GTA Fencing Company",
      "logo": {
        "@type": "ImageObject",
        "url": "https://www.gtafencingcompany.com/Images/gta-fencing-logo.png",
        "width": 150,
        "height": 50
      }
    },
    "datePublished": post.publishDate,
    "dateModified": post.lastModified,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": post.canonicalUrl
    },
    "articleSection": post.category,
    "keywords": post.tags.join(", "),
    "wordCount": post.content.split(' ').length,
    "timeRequired": `PT${post.readingTime}M`
  };

  return (
    <div className="bg-white">
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />

      {/* Hero Section */}
      <section className="bg-deep-navy text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Breadcrumb */}
            <nav className="mb-6 text-sm">
              <Link href="/" className="text-warm-off-white hover:text-premium-gold transition-colors">
                Home
              </Link>
              <span className="mx-2 text-warm-off-white">/</span>
              <Link href="/blog/" className="text-warm-off-white hover:text-premium-gold transition-colors">
                Blog
              </Link>
              <span className="mx-2 text-warm-off-white">/</span>
              <span className="text-premium-gold">{post.title}</span>
            </nav>

            {/* Article Header */}
            <div className="mb-8">
              <div className="flex flex-wrap items-center gap-4 mb-4 text-sm">
                <span className="bg-premium-gold text-deep-navy px-3 py-1 rounded-full font-semibold">
                  {post.category}
                </span>
                <time dateTime={post.publishDate} className="text-warm-off-white">
                  {new Date(post.publishDate).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </time>
                <span className="text-warm-off-white">
                  {post.readingTime} min read
                </span>
              </div>
              
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 leading-tight">
                {post.title}
              </h1>
              
              <p className="text-lg md:text-xl text-warm-off-white leading-relaxed">
                {post.excerpt}
              </p>
            </div>

            {/* Featured Image */}
            <div className="relative h-64 md:h-96 rounded-lg overflow-hidden">
              <Image
                src={post.image.url}
                alt={post.image.alt}
                fill
                className="object-cover"
                priority
              />
            </div>
          </div>
        </div>
      </section>

      {/* Article Content */}
      <article className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="prose prose-lg max-w-none">
              <ReactMarkdown
                components={{
                  h2: ({ children }) => (
                    <h2 className="text-2xl md:text-3xl font-bold text-deep-navy mt-12 mb-6 first:mt-0">
                      {children}
                    </h2>
                  ),
                  h3: ({ children }) => (
                    <h3 className="text-xl md:text-2xl font-semibold text-deep-navy mt-8 mb-4">
                      {children}
                    </h3>
                  ),
                  p: ({ children }) => (
                    <p className="text-rich-charcoal leading-relaxed mb-6">
                      {children}
                    </p>
                  ),
                  a: ({ href, children }) => (
                    <Link 
                      href={href || '#'} 
                      className="text-premium-gold hover:text-deep-navy font-semibold transition-colors duration-200 underline"
                    >
                      {children}
                    </Link>
                  ),
                  ul: ({ children }) => (
                    <ul className="list-disc list-inside mb-6 space-y-2 text-rich-charcoal">
                      {children}
                    </ul>
                  ),
                  ol: ({ children }) => (
                    <ol className="list-decimal list-inside mb-6 space-y-2 text-rich-charcoal">
                      {children}
                    </ol>
                  ),
                  li: ({ children }) => (
                    <li className="leading-relaxed">
                      {children}
                    </li>
                  ),
                  blockquote: ({ children }) => (
                    <blockquote className="border-l-4 border-premium-gold pl-6 my-8 italic text-rich-charcoal bg-soft-beige p-6 rounded-r-lg">
                      {children}
                    </blockquote>
                  )
                }}
              >
                {post.content}
              </ReactMarkdown>
            </div>

            {/* Tags */}
            <div className="mt-12 pt-8 border-t border-warm-off-white">
              <h3 className="text-lg font-semibold text-deep-navy mb-4">Tags:</h3>
              <div className="flex flex-wrap gap-2">
                {post.tags.map((tag) => (
                  <span
                    key={tag}
                    className="bg-soft-beige text-rich-charcoal px-3 py-1 rounded-full text-sm font-medium"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </article>

      {/* Related Posts Section */}
      <section className="py-16 bg-warm-off-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl md:text-3xl font-bold text-deep-navy mb-8 text-center">
              Related Articles
            </h2>
            <div className="grid md:grid-cols-2 gap-8">
              {Object.values(blogPosts)
                .filter(relatedPost => relatedPost.slug !== post.slug)
                .slice(0, 2)
                .map((relatedPost) => (
                  <article key={relatedPost.slug} className="bg-white rounded-lg shadow-md overflow-hidden">
                    <div className="relative h-48">
                      <Image
                        src={relatedPost.image.url}
                        alt={relatedPost.image.alt}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div className="p-6">
                      <div className="flex items-center gap-2 mb-3">
                        <span className="bg-premium-gold text-deep-navy px-2 py-1 rounded text-xs font-semibold">
                          {relatedPost.category}
                        </span>
                        <span className="text-sm text-rich-charcoal">
                          {relatedPost.readingTime} min read
                        </span>
                      </div>
                      <h3 className="text-xl font-bold text-deep-navy mb-3">
                        <Link
                          href={`/blog/${relatedPost.slug}/`}
                          className="hover:text-premium-gold transition-colors duration-200"
                        >
                          {relatedPost.title}
                        </Link>
                      </h3>
                      <p className="text-rich-charcoal mb-4 line-clamp-3">
                        {relatedPost.excerpt}
                      </p>
                      <Link
                        href={`/blog/${relatedPost.slug}/`}
                        className="text-premium-gold hover:text-deep-navy font-semibold transition-colors duration-200"
                      >
                        Read More →
                      </Link>
                    </div>
                  </article>
                ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-premium-gold text-center">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-4 text-deep-navy">Ready to Start Your Fencing Project?</h2>
          <p className="text-lg mb-8 text-deep-navy max-w-2xl mx-auto">
            Get expert advice and a free quote for your fencing needs. Our team is ready to help you choose the perfect fence for your property.
          </p>
          <CTA text="Get Your Free Quote" link="/contact" />
        </div>
      </section>
    </div>
  );
}

// Generate static params for all blog posts
export async function generateStaticParams() {
  return Object.keys(blogPosts).map((slug) => ({
    slug,
  }));
}

// Generate metadata for SEO
export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params;
  const post = blogPosts[slug];

  if (!post) {
    return {
      title: 'Blog Post Not Found',
    };
  }

  return {
    title: post.seoTitle,
    description: post.metaDescription,
    keywords: post.tags.join(', '),
    authors: [{ name: post.author.name, url: post.author.url }],
    openGraph: {
      title: post.seoTitle,
      description: post.metaDescription,
      type: 'article',
      publishedTime: post.publishDate,
      modifiedTime: post.lastModified,
      authors: [post.author.name],
      images: [
        {
          url: `https://www.gtafencingcompany.com${post.image.url}`,
          width: post.image.width,
          height: post.image.height,
          alt: post.image.alt,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: post.seoTitle,
      description: post.metaDescription,
      images: [`https://www.gtafencingcompany.com${post.image.url}`],
    },
    robots: {
      index: true,
      follow: true,
    },
    alternates: {
      canonical: post.canonicalUrl,
    },
  };
}
